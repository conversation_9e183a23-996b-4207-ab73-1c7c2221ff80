"""
Tools API endpoints
"""
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from src.config.database import get_database
from src.services.tool_service import ToolService
from src.config.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()


class ToolInfo(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]
    category: str
    enabled: bool


class ToolExecutionRequest(BaseModel):
    tool_name: str
    arguments: Dict[str, Any]
    project_id: Optional[int] = None


class ToolExecutionResponse(BaseModel):
    success: bool
    result: Any
    error: Optional[str] = None
    execution_time: float


@router.get("/", response_model=List[ToolInfo])
async def list_tools():
    """List all available tools"""
    try:
        service = ToolService()
        tools = service.list_tools()
        return tools
    except Exception as e:
        logger.error("Failed to list tools", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list tools"
        )


@router.get("/{tool_name}", response_model=ToolInfo)
async def get_tool_info(tool_name: str):
    """Get information about a specific tool"""
    try:
        service = ToolService()
        tool_info = service.get_tool_info(tool_name)
        if not tool_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tool not found"
            )
        return tool_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get tool info", tool_name=tool_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get tool info"
        )


@router.post("/execute", response_model=ToolExecutionResponse)
async def execute_tool(
    request: ToolExecutionRequest,
    db: Session = Depends(get_database)
):
    """Execute a tool with given arguments"""
    try:
        service = ToolService(db)
        result = await service.execute_tool(
            tool_name=request.tool_name,
            arguments=request.arguments,
            project_id=request.project_id
        )
        logger.info("Tool executed", tool_name=request.tool_name, success=result.success)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to execute tool", tool_name=request.tool_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute tool"
        )


@router.post("/validate")
async def validate_tool_call(
    tool_name: str,
    arguments: Dict[str, Any]
):
    """Validate tool call arguments"""
    try:
        service = ToolService()
        validation_result = service.validate_tool_call(tool_name, arguments)
        return validation_result
    except Exception as e:
        logger.error("Failed to validate tool call", tool_name=tool_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate tool call"
        )


@router.get("/categories/")
async def get_tool_categories():
    """Get all tool categories"""
    try:
        service = ToolService()
        categories = service.get_tool_categories()
        return {"categories": categories}
    except Exception as e:
        logger.error("Failed to get tool categories", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get tool categories"
        )

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
# sqlite3 is built into Python

# Vector database and embeddings
chromadb==0.4.24
sentence-transformers==2.2.2
numpy<2.0.0

# Code parsing and analysis
tree-sitter==0.20.4
tree-sitter-python==0.23.6
tree-sitter-javascript==0.23.0
tree-sitter-typescript==0.23.0
tree-sitter-java==0.23.2
tree-sitter-cpp==0.23.4
tree-sitter-c==0.23.0
tree-sitter-rust==0.23.0
tree-sitter-go==0.23.3

# Task queue and background processing
celery==5.3.4
redis==5.0.1

# HTTP clients and API integration
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# File watching and monitoring
watchdog==3.0.0

# JSON and data processing
pydantic==2.5.0
pydantic-settings==2.1.0
orjson>=3.9.12

# Logging and monitoring
structlog==23.2.0
rich==13.7.0

# Security and authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
pathlib2==2.3.7
typing-extensions==4.8.0

# Docker and containerization support
docker==6.1.3

# Git integration
GitPython==3.1.40

# Graph analysis
networkx==3.2.1

# Code quality and analysis
pylint==3.0.3
mypy==1.7.1

# WebSocket support for real-time updates
websockets==12.0

# System monitoring
psutil==5.9.6

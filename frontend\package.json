{"name": "echocode-frontend", "version": "1.0.0", "description": "EchoCode AI Coder Frontend", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "axios": "^1.6.2", "react-query": "^3.39.3", "@monaco-editor/react": "^4.6.0", "react-split-pane": "^0.1.92", "react-icons": "^4.12.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-split-pane": "^0.1.3"}, "proxy": "http://localhost:8000"}
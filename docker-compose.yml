services:
  # Main EchoCode application
  echocode:
    build: .
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - OLLAMA_URL=http://ollama:11434
      - DATABASE_URL=sqlite:///app/data/echocode.db
      - CHROMA_URL=http://chromadb:8000
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
      - DEFAULT_CODING_MODEL=deepseek/deepseek-r1-distill-llama-70b
      - DEFAULT_VISION_MODEL=google/gemini-flash-1.5-8b
      - OLLAMA_MODEL=nomic-embed-text
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./projects:/app/projects
      - /var/run/docker.sock:/var/run/docker.sock  # For sandboxed execution
    depends_on:
      - redis
      - ollama
      - chromadb
    restart: unless-stopped
    networks:
      - echocode-network

  # Redis for task queue and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - echocode-network

  # Ollama for local embeddings
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    restart: unless-stopped
    networks:
      - echocode-network
    # Pull embedding model on startup
    command: >
      sh -c "ollama serve & 
             sleep 10 && 
             ollama pull nomic-embed-text && 
             wait"

  # Celery worker for background tasks
  celery-worker:
    build: .
    command: celery -A src.tasks.celery_app worker --loglevel=info
    environment:
      - REDIS_URL=redis://redis:6379
      - OLLAMA_URL=http://ollama:11434
      - DATABASE_URL=sqlite:///app/data/echocode.db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./projects:/app/projects
    depends_on:
      - redis
      - ollama
    restart: unless-stopped
    networks:
      - echocode-network

  # Celery beat for scheduled tasks
  celery-beat:
    build: .
    command: celery -A src.tasks.celery_app beat --loglevel=info
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=sqlite:///app/data/echocode.db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - echocode-network

  # ChromaDB Vector Database
  chromadb:
    image: chromadb/chroma:latest
    container_name: echocode_chromadb
    ports:
      - "8001:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - echocode-network
    restart: unless-stopped

  # React Frontend
  frontend:
    build: ./frontend
    container_name: echocode_frontend
    ports:
      - "3000:80"
    depends_on:
      - echocode
    networks:
      - echocode-network
    restart: unless-stopped

volumes:
  redis_data:
  ollama_data:
  chromadb_data:

networks:
  echocode-network:
    driver: bridge

import React from 'react';
import { useQuery } from 'react-query';
import { FiCpu, FiPlay, FiPause, FiStop, FiActivity } from 'react-icons/fi';
import { apiClient } from '../services/api';

export const Agents: React.FC = () => {
  const { data: agentRuns, isLoading } = useQuery('agent-runs', () => apiClient.getAgentRuns());
  const { data: agentStatus } = useQuery('agent-status', () => apiClient.getAgentStatus());

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'stopped':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">AI Agents</h1>
        <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
          <FiPlay className="h-4 w-4" />
          <span>Start Agent</span>
        </button>
      </div>

      {/* Agent status overview */}
      {agentStatus && (
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <FiActivity className="h-5 w-5 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Runs</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{agentStatus.total_runs}</p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <FiCpu className="h-5 w-5 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{agentStatus.active_runs}</p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <FiActivity className="h-5 w-5 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{agentStatus.completed_runs}</p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <FiActivity className="h-5 w-5 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Failed</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{agentStatus.failed_runs}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Agent runs list */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Agent Runs</h3>
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : agentRuns && agentRuns.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {agentRuns.map((run) => (
              <div key={run.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      <FiCpu className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {run.agent_type}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Project ID: {run.project_id} • {run.iterations} iterations
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(run.status)}`}>
                      {run.status}
                    </span>
                    <div className="flex space-x-1">
                      {run.status === 'running' && (
                        <button className="p-1 text-gray-400 hover:text-yellow-600 transition-colors" title="Pause">
                          <FiPause className="h-4 w-4" />
                        </button>
                      )}
                      {run.status === 'paused' && (
                        <button className="p-1 text-gray-400 hover:text-green-600 transition-colors" title="Resume">
                          <FiPlay className="h-4 w-4" />
                        </button>
                      )}
                      {(run.status === 'running' || run.status === 'paused') && (
                        <button className="p-1 text-gray-400 hover:text-red-600 transition-colors" title="Stop">
                          <FiStop className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  Started: {new Date(run.started_at).toLocaleString()}
                  {run.completed_at && (
                    <span> • Completed: {new Date(run.completed_at).toLocaleString()}</span>
                  )}
                </div>
                {run.error_message && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                    Error: {run.error_message}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="px-6 py-8 text-center">
            <FiCpu className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No agent runs</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Start an agent to begin autonomous coding.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

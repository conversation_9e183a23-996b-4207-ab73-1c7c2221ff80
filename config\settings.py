"""
EchoCode Configuration Settings
"""
import os
from pathlib import Path
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    app_name: str = "EchoCode AI Coder"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    
    # Database
    database_url: str = Field(default="sqlite:///./data/echocode.db", env="DATABASE_URL")
    
    # Redis
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # OpenRouter API
    openrouter_api_key: str = Field(env="OPENROUTER_API_KEY")
    openrouter_base_url: str = Field(default="https://openrouter.ai/api/v1", env="OPENROUTER_BASE_URL")
    
    # Ollama (Local embeddings)
    ollama_url: str = Field(default="http://localhost:11434", env="OLLAMA_URL")
    ollama_model: str = Field(default="nomic-embed-text", env="OLLAMA_MODEL")
    
    # AI Models
    default_coding_model: str = Field(default="deepseek/deepseek-r1", env="DEFAULT_CODING_MODEL")
    default_vision_model: str = Field(default="google/gemini-flash-2.0-exp", env="DEFAULT_VISION_MODEL")
    
    # File System
    projects_dir: Path = Field(default=Path("./projects"), env="PROJECTS_DIR")
    data_dir: Path = Field(default=Path("./data"), env="DATA_DIR")
    logs_dir: Path = Field(default=Path("./logs"), env="LOGS_DIR")
    
    # Indexing
    max_file_size_mb: int = Field(default=10, env="MAX_FILE_SIZE_MB")
    supported_extensions: List[str] = Field(
        default=[".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cpp", ".c", ".h", ".rs", ".go", ".php", ".rb", ".cs", ".swift", ".kt", ".scala", ".clj", ".hs", ".ml", ".r", ".sql", ".html", ".css", ".scss", ".less", ".vue", ".svelte", ".md", ".txt", ".json", ".yaml", ".yml", ".toml", ".xml", ".sh", ".bat", ".ps1"],
        env="SUPPORTED_EXTENSIONS"
    )
    
    # Vector Database
    chroma_persist_directory: str = Field(default="./data/chroma", env="CHROMA_PERSIST_DIRECTORY")
    embedding_dimension: int = Field(default=768, env="EMBEDDING_DIMENSION")
    
    # Context Management
    max_context_tokens: int = Field(default=32000, env="MAX_CONTEXT_TOKENS")
    context_overlap_tokens: int = Field(default=200, env="CONTEXT_OVERLAP_TOKENS")
    
    # Continuous Loop
    max_loop_iterations: int = Field(default=50, env="MAX_LOOP_ITERATIONS")
    loop_timeout_minutes: int = Field(default=30, env="LOOP_TIMEOUT_MINUTES")
    
    # Tool Execution
    tool_timeout_seconds: int = Field(default=60, env="TOOL_TIMEOUT_SECONDS")
    enable_sandboxing: bool = Field(default=True, env="ENABLE_SANDBOXING")
    
    # Rate Limiting
    openrouter_requests_per_minute: int = Field(default=60, env="OPENROUTER_REQUESTS_PER_MINUTE")
    openrouter_requests_per_day: int = Field(default=1000, env="OPENROUTER_REQUESTS_PER_DAY")
    
    # Security
    allowed_hosts: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


def create_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        settings.projects_dir,
        settings.data_dir,
        settings.logs_dir,
        Path(settings.chroma_persist_directory).parent
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


# Create directories on import
create_directories()

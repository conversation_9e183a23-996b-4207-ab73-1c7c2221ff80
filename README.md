# EchoCode AI Coder

Advanced asynchronous AI coding assistant with intelligent codebase indexing, continuous self-improving loops, and mock tool calling for OpenRouter models.

## 🚀 Features

### Core Capabilities
- **Intelligent Codebase Indexing**: Multi-layer indexing combining semantic embeddings, structural analysis, and dependency mapping
- **Continuous Loop System**: Self-improving agent that codes, tests, analyzes errors, and iterates automatically
- **Mock Tool Calling**: Workaround for OpenRouter models that don't support native tool calling
- **Real-time Web Interface**: Monitor and control AI agents with live updates
- **Multi-Model Support**: DeepSeek R1 for coding, Gemini Flash 2.0 for vision, local Ollama for embeddings

### Advanced Features
- **Context-Aware Code Generation**: Full codebase understanding for every AI decision
- **Error Analysis & Recovery**: Multi-level error handling with automatic recovery
- **Vector Search**: Semantic code search using ChromaDB and local embeddings
- **File Management**: Safe file operations with backup and recovery
- **Background Processing**: Celery-based task queue for long-running operations

## 🏗️ Architecture

### Technology Stack
- **Backend**: FastAPI (Python) - High performance async operations
- **Frontend**: React with TypeScript - Modern, responsive UI
- **Database**: SQLite for persistence + ChromaDB for vector storage
- **LLM Integration**: OpenRouter API (DeepSeek R1, Gemini Flash 2.0)
- **Embeddings**: Local Ollama models (nomic-embed-text)
- **Code Analysis**: TreeSitter for parsing and AST generation
- **Task Queue**: Celery with Redis for background operations

### Key Components
- **Codebase Indexing**: Semantic + structural + dependency indexing
- **Agent Orchestrator**: Manages continuous coding loops
- **Tool Registry**: Executes tools through JSON-based mock calling
- **Context Assembler**: Intelligent context selection for LLM queries
- **Error Analyzer**: Detects and resolves compilation/runtime errors

## 🐳 Quick Start with Docker

### Prerequisites
- Docker and Docker Compose
- OpenRouter API key
- 8GB+ RAM recommended

### 1. Clone and Setup
```bash
git clone https://github.com/R2-ally/EchoCode.git
cd EchoCode

# Copy environment template
cp .env.example .env

# Edit .env and add your OpenRouter API key
# OPENROUTER_API_KEY=your_key_here
```

### 2. Start Services
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f echocode
```

### 3. Access Application
- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### 4. Create Your First Project
1. Open the web interface
2. Go to "Projects" tab
3. Click "New Project"
4. Enter project name and path to your code directory
5. Click "Create Project"
6. Click "Scan" to index the project files

## 🛠️ Development Setup

### Local Development
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start Redis (required)
docker run -d -p 6379:6379 redis:7-alpine

# Start Ollama (required)
docker run -d -p 11434:11434 ollama/ollama:latest

# Pull embedding model
docker exec -it <ollama-container> ollama pull nomic-embed-text

# Start FastAPI server
python main.py
```

### Frontend Development
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

## 📖 Usage Guide

### Creating Projects
1. **Add Project**: Specify name and path to your codebase
2. **Scan Files**: Index all supported files in the project
3. **Start Agent**: Begin continuous coding loops

### AI Agent Operations
- **Continuous Coder**: Autonomously improves code quality
- **Error Analyzer**: Detects and fixes compilation/runtime errors
- **Code Generator**: Creates new code based on requirements
- **Refactoring Agent**: Improves code structure and performance

### Tool System
Available tools for AI agents:
- `read_file`: Read file contents
- `write_file`: Write/modify files
- `run_command`: Execute shell commands (sandboxed)
- `search_codebase`: Semantic code search
- `analyze_error`: Error analysis and suggestions

### Mock Tool Calling
Since OpenRouter models don't support native tool calling, EchoCode implements a JSON-based system:

```json
{
  "tool_calls": [
    {
      "name": "read_file",
      "arguments": {"file_path": "src/main.py"}
    }
  ],
  "response": "I need to read the main file to understand the structure."
}
```

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```bash
# Required
OPENROUTER_API_KEY=your_key_here

# Models
DEFAULT_CODING_MODEL=deepseek/deepseek-r1
DEFAULT_VISION_MODEL=google/gemini-flash-2.0-exp

# Performance
MAX_CONTEXT_TOKENS=32000
MAX_LOOP_ITERATIONS=50

# File Processing
MAX_FILE_SIZE_MB=10
SUPPORTED_EXTENSIONS=.py,.js,.ts,.java,.cpp,.rs,.go
```

### Supported File Types
- **Languages**: Python, JavaScript, TypeScript, Java, C++, Rust, Go, PHP, Ruby, C#
- **Config**: JSON, YAML, TOML, XML
- **Web**: HTML, CSS, SCSS, Vue, Svelte
- **Docs**: Markdown, Text files

## 🔍 API Reference

### Projects
- `GET /api/projects/` - List projects
- `POST /api/projects/` - Create project
- `POST /api/projects/{id}/scan` - Scan project files

### Files
- `GET /api/files/project/{id}` - List project files
- `GET /api/files/{id}/content` - Get file content
- `PUT /api/files/{id}/content` - Update file content

### Agents
- `POST /api/agents/start` - Start agent
- `GET /api/agents/runs` - List agent runs
- `POST /api/agents/runs/{id}/control` - Control agent (pause/resume/stop)

### Tools
- `GET /api/tools/` - List available tools
- `POST /api/tools/execute` - Execute tool

## 🧪 Testing

```bash
# Run backend tests
pytest

# Run frontend tests
cd frontend && npm test

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 Monitoring

### System Health
- **Health Endpoint**: `/health`
- **Metrics**: `/api/status`
- **Agent Status**: `/api/agents/status`

### Logs
- **Application**: `./logs/echocode.log`
- **Docker**: `docker-compose logs`

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenRouter** for AI model access
- **Ollama** for local embeddings
- **ChromaDB** for vector storage
- **TreeSitter** for code parsing
- **FastAPI** and **React** communities

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/R2-ally/EchoCode/issues)
- **Discussions**: [GitHub Discussions](https://github.com/R2-ally/EchoCode/discussions)
- **Documentation**: [Wiki](https://github.com/R2-ally/EchoCode/wiki)

---

**EchoCode** - Where AI meets intelligent code understanding 🚀

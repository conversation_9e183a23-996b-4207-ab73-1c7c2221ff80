"""
Agents API endpoints
"""
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from src.config.database import get_database
from src.services.agent_service import AgentService
from src.config.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()


class AgentRunResponse(BaseModel):
    id: int
    project_id: int
    agent_type: str
    status: str
    config: Dict[str, Any]
    started_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    iterations: int = 0
    
    class Config:
        from_attributes = True


class StartAgentRequest(BaseModel):
    project_id: int
    agent_type: str = "continuous_coder"
    config: Dict[str, Any] = {}


class AgentControlRequest(BaseModel):
    action: str  # "pause", "resume", "stop"


@router.get("/runs", response_model=List[AgentRunResponse])
async def list_agent_runs(
    project_id: Optional[int] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_database)
):
    """List agent runs"""
    try:
        service = AgentService(db)
        runs = service.list_agent_runs(
            project_id=project_id,
            status=status,
            skip=skip,
            limit=limit
        )
        return runs
    except Exception as e:
        logger.error("Failed to list agent runs", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list agent runs"
        )


@router.post("/start", response_model=AgentRunResponse)
async def start_agent(
    request: StartAgentRequest,
    db: Session = Depends(get_database)
):
    """Start a new agent run"""
    try:
        service = AgentService(db)
        agent_run = await service.start_agent(
            project_id=request.project_id,
            agent_type=request.agent_type,
            config=request.config
        )
        logger.info("Agent started", run_id=agent_run.id, project_id=request.project_id)
        return agent_run
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to start agent", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start agent"
        )


@router.get("/runs/{run_id}", response_model=AgentRunResponse)
async def get_agent_run(
    run_id: int,
    db: Session = Depends(get_database)
):
    """Get agent run by ID"""
    try:
        service = AgentService(db)
        agent_run = service.get_agent_run(run_id)
        if not agent_run:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent run not found"
            )
        return agent_run
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent run", run_id=run_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent run"
        )


@router.post("/runs/{run_id}/control")
async def control_agent(
    run_id: int,
    request: AgentControlRequest,
    db: Session = Depends(get_database)
):
    """Control agent execution (pause, resume, stop)"""
    try:
        service = AgentService(db)
        result = await service.control_agent(run_id, request.action)
        logger.info("Agent control action", run_id=run_id, action=request.action)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to control agent", run_id=run_id, action=request.action, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to control agent"
        )


@router.get("/runs/{run_id}/logs")
async def get_agent_logs(
    run_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_database)
):
    """Get agent run logs"""
    try:
        service = AgentService(db)
        logs = service.get_agent_logs(run_id, skip=skip, limit=limit)
        return {"logs": logs}
    except Exception as e:
        logger.error("Failed to get agent logs", run_id=run_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent logs"
        )


@router.get("/status")
async def get_agent_status():
    """Get overall agent system status"""
    try:
        service = AgentService()
        status_info = service.get_system_status()
        return status_info
    except Exception as e:
        logger.error("Failed to get agent status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent status"
        )

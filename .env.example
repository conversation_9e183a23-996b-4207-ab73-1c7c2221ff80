# EchoCode Configuration

# Application
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Database
DATABASE_URL=sqlite:///./data/echocode.db

# Redis
REDIS_URL=redis://localhost:6379

# OpenRouter API (Required)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Ollama (Local embeddings)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=nomic-embed-text

# AI Models
DEFAULT_CODING_MODEL=deepseek/deepseek-r1
DEFAULT_VISION_MODEL=google/gemini-flash-2.0-exp

# File System
PROJECTS_DIR=./projects
DATA_DIR=./data
LOGS_DIR=./logs

# Indexing
MAX_FILE_SIZE_MB=10
CHROMA_PERSIST_DIRECTORY=./data/chroma

# Context Management
MAX_CONTEXT_TOKENS=32000
CONTEXT_OVERLAP_TOKENS=200

# Continuous Loop
MAX_LOOP_ITERATIONS=50
LOOP_TIMEOUT_MINUTES=30

# Tool Execution
TOOL_TIMEOUT_SECONDS=60
ENABLE_SANDBOXING=true

# Rate Limiting
OPENROUTER_REQUESTS_PER_MINUTE=60
OPENROUTER_REQUESTS_PER_DAY=1000

# Security
ALLOWED_HOSTS=*
CORS_ORIGINS=*

{"version": 3, "file": "no-inferrable-types.js", "sourceRoot": "", "sources": ["../../src/rules/no-inferrable-types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAE1D,8CAAgC;AAUhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,6GAA6G;YAC/G,WAAW,EAAE,OAAO;SACrB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,gBAAgB,EACd,mFAAmF;SACtF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;YACvB,gBAAgB,EAAE,KAAK;SACxB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CAAC;QACtD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,cAAc,CACrB,IAAyB,EACzB,QAAgB;YAEhB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAChD,OAAO,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAClD;YAED,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAC9B,CAAC;QACJ,CAAC;QACD,SAAS,SAAS,CAAC,IAAyB,EAAE,QAAgB;YAC5D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CACvE,CAAC;QACJ,CAAC;QACD,SAAS,YAAY,CACnB,IAAyB,EACzB,GAAG,KAAe;YAElB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CACrE,CAAC;QACJ,CAAC;QACD,SAAS,cAAc,CACrB,IAAyB,EACzB,GAAG,SAAmB;YAEtB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAClC,CAAC;QACJ,CAAC;QAWD,MAAM,UAAU,GAAG;YACjB,CAAC,sBAAc,CAAC,eAAe,CAAC,EAAE,QAAQ;YAC1C,CAAC,sBAAc,CAAC,gBAAgB,CAAC,EAAE,SAAS;YAC5C,CAAC,sBAAc,CAAC,eAAe,CAAC,EAAE,QAAQ;YAC1C,CAAC,sBAAc,CAAC,aAAa,CAAC,EAAE,MAAM;YACtC,CAAC,sBAAc,CAAC,eAAe,CAAC,EAAE,QAAQ;YAC1C,CAAC,sBAAc,CAAC,eAAe,CAAC,EAAE,QAAQ;YAC1C,CAAC,sBAAc,CAAC,kBAAkB,CAAC,EAAE,WAAW;SACjD,CAAC;QAEF;;WAEG;QACH,SAAS,YAAY,CACnB,UAA6B,EAC7B,IAAyB;YAEzB,QAAQ,UAAU,CAAC,IAAI,EAAE;gBACvB,KAAK,sBAAc,CAAC,eAAe,CAAC,CAAC;oBACnC,gDAAgD;oBAChD,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC;wBAC7C,CAAC,CAAC,IAAI,CAAC,QAAQ;wBACf,CAAC,CAAC,IAAI,CAAC;oBAET,OAAO,CACL,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC;wBACvC,CAAC,aAAa,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;4BAC5C,QAAQ,IAAI,aAAa,CAAC,CAC7B,CAAC;iBACH;gBAED,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,OAAO,CACL,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC;wBACzB,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC;wBAC/B,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAC3B,CAAC;gBAEJ,KAAK,sBAAc,CAAC,eAAe,CAAC,CAAC;oBACnC,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;wBAClD,CAAC,CAAC,IAAI,CAAC,QAAQ;wBACf,CAAC,CAAC,IAAI,CAAC;oBAET,OAAO,CACL,YAAY,CAAC,aAAa,EAAE,UAAU,EAAE,KAAK,CAAC;wBAC9C,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC;wBACvC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CACnC,CAAC;iBACH;gBAED,KAAK,sBAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;gBAEpE,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,CACL,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC;wBAC9B,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC;wBACzB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAC7C,CAAC;gBAEJ,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAExC,KAAK,sBAAc,CAAC,eAAe,CAAC,CAAC;oBACnC,IACE,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBACtD,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EACrC;wBACA,MAAM,eAAe,GACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;4BACpC,IAAI,CAAC,KAAK,YAAY,MAAM,CAAC;wBAC/B,MAAM,eAAe,GACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;4BAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;wBAChC,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAEpD,OAAO,eAAe,IAAI,YAAY,IAAI,eAAe,CAAC;qBAC3D;oBAED,OAAO,KAAK,CAAC;iBACd;gBAED,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,CACL,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAChE,CAAC;aACL;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;WAEG;QACH,SAAS,oBAAoB,CAC3B,IAG+B,EAC/B,QAA+C,EAC/C,QAAgD;YAEhD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBACtD,OAAO;aACR;YAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE;gBACpD,OAAO;aACR;YAED,MAAM,IAAI,GACR,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC7D,CAAC,CAAC,mCAAmC;oBACnC,QAAQ;gBACV,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE/C,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,kBAAkB;gBAC7B,IAAI,EAAE;oBACJ,IAAI;iBACL;gBACD,CAAC,GAAG,CAAC,KAAK;oBACR,IACE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;wBAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACrB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,EAClE;wBACA,MAAM,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAE,CAAC,CAAC;qBAC1D;oBACD,MAAM,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/B,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAiC;YAEjC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACZ,OAAO;aACR;YACD,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;QAED,SAAS,0BAA0B,CACjC,IAGoC;YAEpC,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACpC,OAAO;aACR;YAEC,IAAI,CAAC,MAAM,CAAC,MAAM,CAChB,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBAC/C,KAAK,CAAC,IAAI;gBACV,KAAK,CAAC,KAAK,CAEhB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAChB,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAiC;YAEjC,6DAA6D;YAC7D,iDAAiD;YACjD,sDAAsD;YACtD,0CAA0C;YAC1C,IAAI,gBAAgB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACtD,OAAO;aACR;YACD,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO;YACL,kBAAkB,EAAE,yBAAyB;YAC7C,kBAAkB,EAAE,0BAA0B;YAC9C,mBAAmB,EAAE,0BAA0B;YAC/C,uBAAuB,EAAE,0BAA0B;YACnD,kBAAkB,EAAE,yBAAyB;SAC9C,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}
{"version": 3, "file": "no-mixed-enums.js", "sourceRoot": "", "sources": ["../../src/rules/no-mixed-enums.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oEAAkE;AAElE,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,IAAK,WAIJ;AAJD,WAAK,WAAW;IACd,iDAAM,CAAA;IACN,iDAAM,CAAA;IACN,mDAAO,CAAA;AACT,CAAC,EAJI,WAAW,KAAX,WAAW,QAIf;AAED,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;YACxE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,kDAAkD;SAC1D;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAO5D,SAAS,sBAAsB,CAC7B,IAAgC;;YAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,KAAK,GAAyB;gBAClC,OAAO,EAAE,EAAE;gBACX,eAAe,EAAE,SAAS;aAC3B,CAAC;YACF,IAAI,KAAK,GAAiB,OAAO,CAAC,QAAQ,EAAE,CAAC;YAE7C,KAAK,MAAM,UAAU,IAAI,MAAA,MAAA,MAAA,KAAK,CAAC,KAAK,0CAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,IAAI,mCAAI,EAAE,EAAE;gBAC/D,IACE,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACzD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAClC;oBACA,KAAK,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;oBACxC,MAAM;iBACP;aACF;YAED,OAAO,KAAK,EAAE;gBACZ,MAAA,MAAA,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,IAAI,0CAAE,OAAO,CAAC,UAAU,CAAC,EAAE;oBAC9C,IAAI,UAAU,CAAC,IAAI,KAAK,8BAAc,CAAC,aAAa,EAAE;wBACpD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;qBACrC;gBACH,CAAC,CAAC,CAAC;gBAEH,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;aACrB;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAa;YAC1C,OAAO,OAAO,CAAC,aAAa,CAC1B,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,EACnC,EAAE,CAAC,SAAS,CAAC,UAAU,CACxB;gBACC,CAAC,CAAC,WAAW,CAAC,MAAM;gBACpB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;QACzB,CAAC;QAED,SAAS,mBAAmB,CAC1B,QAAuB;;YAEvB,MAAM,IAAI,GAAG,WAAW,CAAC,iBAAiB,CACxC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CACnD,CAAC;YAEF,MAAM,gBAAgB,GAAG,MAAA,IAAI,CAAC,SAAS,EAAE,0CAAE,gBAAgB,CAAC;YAC5D,IACE,CAAC,gBAAgB;gBACjB,CAAC,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;gBACvC,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EACrC;gBACA,OAAO,SAAS,CAAC;aAClB;YAED,OAAO,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,SAAS,aAAa,CAAC,MAA6B;YAClD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACvB,OAAO,WAAW,CAAC,MAAM,CAAC;aAC3B;YAED,QAAQ,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE;gBAC/B,KAAK,sBAAc,CAAC,OAAO;oBACzB,QAAQ,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;wBACvC,KAAK,QAAQ;4BACX,OAAO,WAAW,CAAC,MAAM,CAAC;wBAC5B,KAAK,QAAQ;4BACX,OAAO,WAAW,CAAC,MAAM,CAAC;wBAC5B;4BACE,OAAO,WAAW,CAAC,OAAO,CAAC;qBAC9B;gBAEH,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,WAAW,CAAC,MAAM,CAAC;gBAE5B;oBACE,OAAO,qBAAqB,CAC1B,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAC7D,CAAC;aACL;QACH,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAgC;YAEhC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAElE,iDAAiD;YACjD,yCAAyC;YACzC,kCAAkC;YAClC,sBAAsB;YACtB,IAAI;YACJ,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE;gBAC9B,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACvD,IAAI,gBAAgB,KAAK,SAAS,EAAE;oBAClC,OAAO,gBAAgB,CAAC;iBACzB;aACF;YAED,oDAAoD;YACpD,oBAAoB;YACpB,oBAAoB;YACpB,IAAI,eAAe,EAAE;gBACnB,OAAO,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;YAED,sCAAsC;YACtC,0BAA0B;YAC1B,6BAA6B;YAC7B,IAAI;YACJ,0BAA0B;YAC1B,6BAA6B;YAC7B,IAAI;YACJ,IACE,IAAI,CAAC,MAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;gBAC3D,IAAI,CAAC,MAAO,CAAC,MAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC1D;gBACA,oEAAoE;gBACpE,qDAAqD;gBACrD,iEAAiE;gBACjE,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjE,MAAM,YAAY,GAAG,WAAW;qBAC7B,mBAAmB,CAAC,MAAM,CAAE;qBAC5B,eAAe,EAAG,CAAC;gBAEtB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;oBACtC,KAAK,MAAM,MAAM,IAAK,WAAkC,CAAC,OAAO,EAAE;wBAChE,OAAO,MAAM,CAAC,WAAW;4BACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CACnB,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,EACjD,EAAE,CAAC,SAAS,CAAC,UAAU,CACxB;gCACD,CAAC,CAAC,WAAW,CAAC,MAAM;gCACpB,CAAC,CAAC,WAAW,CAAC,MAAM;4BACtB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;qBACxB;iBACF;aACF;YAED,2DAA2D;YAC3D,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;QAED,OAAO;YACL,iBAAiB,CAAC,IAAI;;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACxB,OAAO;iBACR;gBAED,IAAI,WAAW,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBACpD,IAAI,WAAW,KAAK,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE;oBACxC,OAAO;iBACR;gBAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjC,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,WAAW,KAAK,WAAW,CAAC,OAAO,EAAE;wBACvC,OAAO;qBACR;oBAED,IAAI,WAAW,KAAK,WAAW,CAAC,MAAM,EAAE;wBACtC,WAAW,aAAX,WAAW,cAAX,WAAW,IAAX,WAAW,GAAK,WAAW,EAAC;qBAC7B;oBAED,IACE,WAAW,KAAK,WAAW;wBAC3B,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,WAAW,CAAC,MAAM,CAAC,EACjE;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,OAAO;4BAClB,IAAI,EAAE,MAAA,MAAM,CAAC,WAAW,mCAAI,MAAM;yBACnC,CAAC,CAAC;wBACH,OAAO;qBACR;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { 
  Search, 
  FileText, 
  Folder, 
  GitBranch, 
  Activity, 
  Zap,
  AlertTriangle,
  TrendingUp,
  Eye
} from 'lucide-react';

interface FileNode {
  path: string;
  name: string;
  type: 'file' | 'directory';
  size?: number;
  language?: string;
  symbol_count?: number;
  importance?: number;
  dependencies?: string[];
  dependents?: string[];
}

interface DependencyEdge {
  source: string;
  target: string;
  type: string;
  strength: number;
}

interface ProjectStats {
  total_files: number;
  total_symbols: number;
  total_dependencies: number;
  languages: string[];
  circular_dependencies: number;
  average_dependencies_per_file: number;
}

interface CodebaseVisualizationProps {
  projectId: number;
}

export const CodebaseVisualization: React.FC<CodebaseVisualizationProps> = ({ projectId }) => {
  const [files, setFiles] = useState<FileNode[]>([]);
  const [dependencies, setDependencies] = useState<DependencyEdge[]>([]);
  const [stats, setStats] = useState<ProjectStats | null>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'tree' | 'graph' | 'stats'>('tree');
  const [loading, setLoading] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    fetchCodebaseData();
  }, [projectId]);

  useEffect(() => {
    if (viewMode === 'graph' && canvasRef.current) {
      drawDependencyGraph();
    }
  }, [viewMode, files, dependencies]);

  const fetchCodebaseData = async () => {
    setLoading(true);
    try {
      // Fetch project statistics
      const statsResponse = await fetch(`/api/indexing/projects/${projectId}/statistics`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch dependency graph
      const graphResponse = await fetch(`/api/indexing/projects/${projectId}/dependency-graph`);
      if (graphResponse.ok) {
        const graphData = await graphResponse.json();
        setFiles(graphData.nodes || []);
        setDependencies(graphData.edges || []);
      }
    } catch (error) {
      console.error('Failed to fetch codebase data:', error);
    } finally {
      setLoading(false);
    }
  };

  const drawDependencyGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Simple force-directed layout
    const nodes = files.map((file, index) => ({
      ...file,
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: 0,
      vy: 0,
      radius: Math.max(5, Math.min(20, (file.symbol_count || 1) * 2))
    }));

    // Draw edges
    ctx.strokeStyle = '#e2e8f0';
    ctx.lineWidth = 1;
    dependencies.forEach(edge => {
      const source = nodes.find(n => n.path === edge.source);
      const target = nodes.find(n => n.path === edge.target);
      
      if (source && target) {
        ctx.beginPath();
        ctx.moveTo(source.x, source.y);
        ctx.lineTo(target.x, target.y);
        ctx.stroke();
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      
      // Color by language
      const colors: { [key: string]: string } = {
        'python': '#3776ab',
        'javascript': '#f7df1e',
        'typescript': '#3178c6',
        'java': '#ed8b00',
        'cpp': '#00599c',
        'rust': '#000000',
        'go': '#00add8'
      };
      
      ctx.fillStyle = colors[node.language || 'unknown'] || '#64748b';
      ctx.fill();
      
      // Highlight selected file
      if (selectedFile === node.path) {
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 3;
        ctx.stroke();
      }
      
      // Draw file name
      ctx.fillStyle = '#1e293b';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(node.name, node.x, node.y + node.radius + 12);
    });
  };

  const filteredFiles = files.filter(file =>
    file.path.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'python': 'bg-blue-500',
      'javascript': 'bg-yellow-500',
      'typescript': 'bg-blue-600',
      'java': 'bg-orange-500',
      'cpp': 'bg-blue-700',
      'rust': 'bg-gray-800',
      'go': 'bg-cyan-500'
    };
    return colors[language] || 'bg-gray-500';
  };

  const getImportanceLevel = (importance: number) => {
    if (importance >= 0.8) return { level: 'Critical', color: 'bg-red-500' };
    if (importance >= 0.6) return { level: 'High', color: 'bg-orange-500' };
    if (importance >= 0.4) return { level: 'Medium', color: 'bg-yellow-500' };
    return { level: 'Low', color: 'bg-green-500' };
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Activity className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading codebase visualization...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Codebase Visualization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'tree' ? 'default' : 'outline'}
                onClick={() => setViewMode('tree')}
                size="sm"
              >
                <Folder className="h-4 w-4 mr-1" />
                Tree
              </Button>
              <Button
                variant={viewMode === 'graph' ? 'default' : 'outline'}
                onClick={() => setViewMode('graph')}
                size="sm"
              >
                <GitBranch className="h-4 w-4 mr-1" />
                Graph
              </Button>
              <Button
                variant={viewMode === 'stats' ? 'default' : 'outline'}
                onClick={() => setViewMode('stats')}
                size="sm"
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                Stats
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {viewMode === 'tree' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* File Tree */}
          <Card>
            <CardHeader>
              <CardTitle>File Structure</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {filteredFiles.map((file) => (
                    <div
                      key={file.path}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedFile === file.path ? 'bg-accent' : 'hover:bg-accent/50'
                      }`}
                      onClick={() => setSelectedFile(file.path)}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {file.type === 'file' ? (
                          <FileText className="h-4 w-4" />
                        ) : (
                          <Folder className="h-4 w-4" />
                        )}
                        <span className="font-medium">{file.name}</span>
                        {file.language && (
                          <Badge className={`text-white ${getLanguageColor(file.language)}`}>
                            {file.language}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        {file.path}
                      </div>
                      
                      {file.symbol_count !== undefined && (
                        <div className="flex items-center gap-4 mt-2 text-sm">
                          <span>Symbols: {file.symbol_count}</span>
                          {file.importance !== undefined && (
                            <Badge className={`text-white ${getImportanceLevel(file.importance).color}`}>
                              {getImportanceLevel(file.importance).level}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* File Details */}
          <Card>
            <CardHeader>
              <CardTitle>File Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedFile ? (
                <ScrollArea className="h-96">
                  {(() => {
                    const file = files.find(f => f.path === selectedFile);
                    if (!file) return <div>File not found</div>;

                    return (
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Information</h4>
                          <div className="space-y-2 text-sm">
                            <div><strong>Path:</strong> {file.path}</div>
                            <div><strong>Type:</strong> {file.type}</div>
                            {file.language && <div><strong>Language:</strong> {file.language}</div>}
                            {file.size && <div><strong>Size:</strong> {file.size} bytes</div>}
                            {file.symbol_count && <div><strong>Symbols:</strong> {file.symbol_count}</div>}
                          </div>
                        </div>

                        {file.dependencies && file.dependencies.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-2">Dependencies</h4>
                            <div className="space-y-1">
                              {file.dependencies.map((dep, index) => (
                                <div key={index} className="text-sm text-muted-foreground">
                                  → {dep}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {file.dependents && file.dependents.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-2">Dependents</h4>
                            <div className="space-y-1">
                              {file.dependents.map((dep, index) => (
                                <div key={index} className="text-sm text-muted-foreground">
                                  ← {dep}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </ScrollArea>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  Select a file to view details
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {viewMode === 'graph' && (
        <Card>
          <CardHeader>
            <CardTitle>Dependency Graph</CardTitle>
          </CardHeader>
          <CardContent>
            <canvas
              ref={canvasRef}
              className="w-full h-96 border rounded"
              onClick={(e) => {
                // Handle canvas clicks to select nodes
                const rect = canvasRef.current?.getBoundingClientRect();
                if (rect) {
                  const x = e.clientX - rect.left;
                  const y = e.clientY - rect.top;
                  // Find clicked node (simplified)
                  // In a real implementation, you'd check if click is within node radius
                }
              }}
            />
            <div className="mt-4 text-sm text-muted-foreground">
              Click on nodes to select files. Node size represents symbol count.
            </div>
          </CardContent>
        </Card>
      )}

      {viewMode === 'stats' && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Files
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.total_files}</div>
              <div className="text-sm text-muted-foreground">Total files</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Symbols
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.total_symbols}</div>
              <div className="text-sm text-muted-foreground">Total symbols</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Dependencies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.total_dependencies}</div>
              <div className="text-sm text-muted-foreground">Total dependencies</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Languages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.languages.map((lang) => (
                  <Badge key={lang} className={`text-white ${getLanguageColor(lang)}`}>
                    {lang}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Circular Dependencies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">{stats.circular_dependencies}</div>
              <div className="text-sm text-muted-foreground">Circular dependencies found</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Avg Dependencies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.average_dependencies_per_file.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground">Per file</div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

"""
EchoCode AI Coder - Main FastAPI Application
"""
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

from config.settings import settings
from src.config.logging_config import setup_logging, get_logger
from src.config.database import init_database
from src.api import projects, files, agents, tools


# Set up logging
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting EchoCode AI Coder", version=settings.app_version)
    
    # Initialize database
    init_database()
    logger.info("Database initialized")
    
    # Initialize services
    # TODO: Initialize embedding service, tool registry, etc.
    
    yield
    
    logger.info("Shutting down EchoCode AI Coder")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Advanced asynchronous AI coding assistant with intelligent codebase indexing and continuous improvement loops",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(projects.router, prefix="/api/projects", tags=["projects"])
app.include_router(files.router, prefix="/api/files", tags=["files"])
app.include_router(agents.router, prefix="/api/agents", tags=["agents"])
app.include_router(tools.router, prefix="/api/tools", tags=["tools"])


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app": settings.app_name,
        "version": settings.app_version
    }


@app.get("/api/status")
async def get_status():
    """Get application status and configuration"""
    return {
        "app_name": settings.app_name,
        "version": settings.app_version,
        "debug": settings.debug,
        "supported_extensions": settings.supported_extensions,
        "max_context_tokens": settings.max_context_tokens,
        "models": {
            "coding": settings.default_coding_model,
            "vision": settings.default_vision_model,
            "embedding": settings.ollama_model
        }
    }


# Mount static files (React frontend)
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    @app.get("/")
    async def serve_frontend():
        """Serve React frontend"""
        index_file = static_dir / "index.html"
        if index_file.exists():
            return FileResponse(index_file)
        else:
            raise HTTPException(status_code=404, detail="Frontend not built")
    
    @app.get("/{path:path}")
    async def serve_frontend_routes(path: str):
        """Serve React frontend for all routes (SPA routing)"""
        # Check if it's an API route
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")
        
        # Serve static files if they exist
        static_file = static_dir / path
        if static_file.exists() and static_file.is_file():
            return FileResponse(static_file)
        
        # Otherwise serve index.html for SPA routing
        index_file = static_dir / "index.html"
        if index_file.exists():
            return FileResponse(index_file)
        else:
            raise HTTPException(status_code=404, detail="Frontend not built")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
